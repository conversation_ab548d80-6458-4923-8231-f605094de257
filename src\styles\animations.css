/* Animation Styles - Keyframes and animation utilities */

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.fade-out {
  animation: fadeOut 0.5s ease-out forwards;
}

.slide-up {
  animation: slideUp 0.5s ease-out forwards;
}

.slide-down {
  animation: slideDown 0.5s ease-out forwards;
}

/* Recording pulse animation */
@keyframes recording-pulse {
  0%, 100% {
    transform: translateX(-50%) scale(1);
    box-shadow: 0 4px 20px rgba(255, 107, 107, 0.6);
  }
  50% {
    transform: translateX(-50%) scale(1.05);
    box-shadow: 0 6px 30px rgba(255, 107, 107, 0.8);
  }
}

/* Loading spin animation */
@keyframes loading-spin {
  0% {
    transform: translateX(-50%) rotate(0deg);
  }
  100% {
    transform: translateX(-50%) rotate(360deg);
  }
}

/* Fade animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Slide animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade in up animation for completion message */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, -40%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}
